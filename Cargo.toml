[package]
name = "iroh-gossip"
version = "0.91.0"
edition = "2021"
readme = "README.md"
description = "gossip messages over broadcast trees"
license = "MIT/Apache-2.0"
authors = ["n0 team"]
repository = "https://github.com/n0-computer/iroh-gossip"
resolver = "2"

# Sadly this also needs to be updated in .github/workflows/ci.yml
rust-version = "1.85"

[lib]
crate-type = ["cdylib", "rlib"]

[lints.rust]
missing_debug_implementations = "warn"

# We use this --cfg for documenting the cargo features on which an API
# is available.  To preview this locally use: RUSTFLAGS="--cfg
# iroh_docsrs cargo +nightly doc --all-features".  We use our own
# iroh_docsrs instead of the common docsrs to avoid also enabling this
# feature in any dependencies, because some indirect dependencies
# require a feature enabled when using `--cfg docsrs` which we can not
# do.  To enable for a crate set `#![cfg_attr(iroh_docsrs,
# feature(doc_cfg))]` in the crate.
unexpected_cfgs = { level = "warn", check-cfg = ["cfg(iroh_docsrs)"] }

[lints.clippy]
unused-async = "warn"

[dependencies]
distributed-topic-tracker-exp ={ git = "https://github.com/rustonbsd/distributed-topic-tracker-exp", branch = "main" }
anyhow = "1"
blake3 = "1.8"
bytes = { version = "1.7", features = ["serde"] }
data-encoding = "2.6.0"
derive_more = { version = "1.0.0", features = [
    "add",
    "debug",
    "deref",
    "display",
    "from",
    "try_into",
    "into",
] }
ed25519-dalek = { version = "2.0.0", features = ["serde", "rand_core"] }
hex = "0.4.3"
indexmap = "2.0"
iroh-metrics = { version = "0.35", default-features = false }
iroh-base = { version = "0.91", default-features = false, features = ["key"] }
n0-future = "0.1.2"
postcard = { version = "1", default-features = false, features = [
    "alloc",
    "use-std",
    "experimental-derive",
] }
rand = { version = "0.8.5", features = ["std_rng"] }
rand_core = "0.6.4"
serde = { version = "1.0.164", features = ["derive"] }

# net dependencies (optional)
futures-lite = { version = "2.3", optional = true }
futures-concurrency = { version = "7.6.1", optional = true }
futures-util = { version = "0.3.30", optional = true }
iroh = { version = "0.91", default-features = false, optional = true }
tokio = { version = "1", optional = true, features = ["io-util", "sync"] }
tokio-util = { version = "0.7.12", optional = true, features = ["codec"] }
tracing = "0.1"
irpc = { version = "0.7.0", optional = true, default-features = false, features = [
    "derive",
    "stream",
    "spans",
] }
n0-snafu = { version = "0.2.1", optional = true }
nested_enum_utils = { version = "0.2.2", optional = true }
snafu = { version = "0.8.5", features = ["rust_1_81"], optional = true }

# rpc dependencies (optional)
quinn = { package = "iroh-quinn", version = "0.14.0", optional = true }

# test-utils dependencies (optional)
rand_chacha = { version = "0.3.1", optional = true }
humantime-serde = { version = "1.1.1", optional = true }

# simulator dependencies (optional)
clap = { version = "4", features = ["derive"], optional = true }
toml = { version = "0.8.20", optional = true }
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
], optional = true }
serde_json = { version = "1", optional = true }
rayon = { version = "1.10.0", optional = true }
comfy-table = { version = "7.1.4", optional = true }


[dev-dependencies]
tokio = { version = "1", features = [
    "io-util",
    "sync",
    "rt",
    "macros",
    "net",
    "fs",
] }
clap = { version = "4", features = ["derive"] }
humantime-serde = { version = "1.1.1" }
iroh = { version = "0.91", default-features = false, features = [
    "metrics",
    "test-utils",
] }
rand_chacha = "0.3.1"
testresult = "0.4.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-test = "0.2.5"
url = "2.4.0"

[features]
default = ["net", "metrics"]
net = [
    "dep:irpc",
    "dep:futures-lite",
    "dep:iroh",
    "dep:tokio",
    "dep:tokio-util",
    "dep:futures-util",
    "dep:futures-concurrency",
    "dep:nested_enum_utils",
    "dep:n0-snafu",
    "dep:snafu",
]
rpc = [
    "dep:irpc",
    "dep:tokio",
    "dep:quinn",
    "dep:nested_enum_utils",
    "dep:n0-snafu",
    "dep:snafu",
    "irpc/rpc",
    "irpc/quinn_endpoint_setup",
]
test-utils = ["dep:rand_chacha", "dep:humantime-serde"]
simulator = [
    "test-utils",
    "dep:tracing-subscriber",
    "dep:toml",
    "dep:clap",
    "dep:serde_json",
    "dep:rayon",
    "dep:comfy-table",
]
metrics = ["iroh-metrics/metrics"]
examples = ["net"]

[[test]]
name = "sim"
path = "tests/sim.rs"
required-features = ["test-utils"]

[[bin]]
name = "sim"
required-features = ["simulator"]

[[example]]
name = "chat"
required-features = ["examples"]

[[example]]
name = "setup"
required-features = ["examples"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "iroh_docsrs"]

[profile.bench]
debug = true

[profile.release]
debug = true
