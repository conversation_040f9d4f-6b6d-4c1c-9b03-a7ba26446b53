name: run simulations
on:
  pull_request:

jobs:
  run_sim:
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install rust stable
        uses: dtolnay/rust-toolchain@stable

      - name: Install sccache
        uses: mozilla-actions/sccache-action@v0.0.9

      - name: Run simulations
        run: |
          git checkout ${{ github.event.pull_request.base.ref }}
          cargo run -q --bin sim --features simulator --release -- run -c simulations/all.toml -o /tmp/sim-main
          git checkout ${{ github.event.pull_request.head.sha }}
          cargo run -q --bin sim --features simulator --release -- run -c simulations/all.toml -o /tmp/sim-pr --baseline /tmp/sim-main |& tee REPORT
          echo "<details><summary>Simulation report</summary>" >> COMMENT
          echo "" >> COMMENT
          echo '```' >> COMMENT
          cat REPORT >> COMMENT
          echo "" >> COMMENT
          echo '```' >> COMMENT
          echo "</details>" >> COMMENT
          echo "" >> COMMENT
          echo "*Last updated: $(date -u +'%Y-%m-%dT%H:%M:%SZ')*" >> COMMENT

      - name: Find Docs Comment
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number || inputs.pr_number }}
          comment-author: "github-actions[bot]"
          body-includes: Simulation report

      - name: Create or Update Docs Comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          issue-number: ${{ github.event.pull_request.number || inputs.pr_number }}
          comment-id: ${{ steps.fc.outputs.comment-id }}
          body-path: COMMENT
          edit-mode: replace
