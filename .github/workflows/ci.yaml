name: CI

on:
  pull_request:
    types: [ 'labeled', 'unlabeled', 'opened', 'synchronize', 'reopened' ]
  merge_group:
  push:
    branches:
      - main

concurrency:
  group: ci-${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  RUST_BACKTRACE: 1
  RUSTFLAGS: -Dwarnings
  RUSTDOCFLAGS: -Dwarnings
  MSRV: "1.85"
  SCCACHE_CACHE_SIZE: "50G"
  IROH_FORCE_STAGING_RELAYS: "1"

jobs:
  tests:
    name: CI Test Suite
    if: "github.event_name != 'pull_request' || ! contains(github.event.pull_request.labels.*.name, 'flaky-test')"
    uses: './.github/workflows/tests.yaml'

  cross_build:
    name: Cross Build Only
    if: "github.event_name != 'pull_request' || ! contains(github.event.pull_request.labels.*.name, 'flaky-test')"
    timeout-minutes: 30
    runs-on: [self-hosted, linux, X64]
    strategy:
      fail-fast: false
      matrix:
        target:
           # cross tests are currently broken vor armv7 and aarch64
           # see https://github.com/cross-rs/cross/issues/1311
          # - armv7-linux-androideabi
          # - aarch64-linux-android
          # Freebsd execution fails in cross
          # - i686-unknown-freebsd # Linking fails :/
          - x86_64-unknown-freebsd
          # Netbsd execution fails to link in cross
          # - x86_64-unknown-netbsd
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install rust stable
      uses: dtolnay/rust-toolchain@stable

    - name: Cleanup Docker
      continue-on-error: true
      run: |
        docker kill $(docker ps -q)

      # See https://github.com/cross-rs/cross/issues/1222
    - uses: taiki-e/install-action@cross

    - name: build
      # cross tests are currently broken vor armv7 and aarch64
      # see https://github.com/cross-rs/cross/issues/1311.  So on
      # those platforms we only build but do not run tests.
      run: cross build --all --target ${{ matrix.target }}
      env:
        RUST_LOG: ${{ runner.debug && 'TRACE' || 'DEBUG'}}

  android_build:
    name: Android Build Only
    if: "github.event_name != 'pull_request' || ! contains(github.event.pull_request.labels.*.name, 'flaky-test')"
    timeout-minutes: 30
    # runs-on: ubuntu-latest
    runs-on: [self-hosted, linux, X64]
    strategy:
      fail-fast: false
      matrix:
        target:
          - aarch64-linux-android
          - armv7-linux-androideabi
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set up Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        target: ${{ matrix.target }}
    - name: Install rustup target
      run: rustup target add ${{ matrix.target }}

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'

    - name: Setup Android SDK
      uses: android-actions/setup-android@v3

    - name: Setup Android NDK
      uses: arqu/setup-ndk@main
      id: setup-ndk
      with:
        ndk-version: r23
        add-to-path: true

    - name: Build
      env:
        ANDROID_NDK_HOME: ${{ steps.setup-ndk.outputs.ndk-path }}
      run: |
        cargo install --version 3.5.4 cargo-ndk
        cargo ndk --target ${{ matrix.target }} build

  cross_test:
    name: Cross Test
    if: "github.event_name != 'pull_request' || ! contains(github.event.pull_request.labels.*.name, 'flaky-test')"
    timeout-minutes: 30
    runs-on: [self-hosted, linux, X64]
    strategy:
      fail-fast: false
      matrix:
        target:
          - i686-unknown-linux-gnu
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install rust stable
      uses: dtolnay/rust-toolchain@stable

    - name: Cleanup Docker
      continue-on-error: true
      run: |
        docker kill $(docker ps -q)

      # See https://github.com/cross-rs/cross/issues/1222
    - uses: taiki-e/install-action@cross

    - name: test
      run: cross test --all --target ${{ matrix.target }} -- --test-threads=12
      env:
        RUST_LOG: ${{ runner.debug && 'TRACE' || 'DEBUG' }}

  wasm_build:
      name: Build wasm32
      runs-on: ubuntu-latest
      env:
          RUSTFLAGS: '--cfg getrandom_backend="wasm_js"'
      steps:
        - name: Checkout sources
          uses: actions/checkout@v4
  
        - name: Install stable toolchain
          uses: dtolnay/rust-toolchain@stable
  
        - name: Add wasm target
          run: rustup target add wasm32-unknown-unknown
  
        - name: Install wasm-tools
          uses: bytecodealliance/actions/wasm-tools/setup@v1
  
        - name: wasm32 build
          run: cargo build --target wasm32-unknown-unknown
  
        # If the Wasm file contains any 'import "env"' declarations, then
        # some non-Wasm-compatible code made it into the final code.
        - name: Ensure no 'import "env"' in iroh-relay Wasm
          run: |
            ! wasm-tools print --skeleton target/wasm32-unknown-unknown/debug/iroh_gossip.wasm | grep 'import "env"'
        

  check_semver:
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install sccache
        uses: mozilla-actions/sccache-action@v0.0.9

      - name: Setup Environment (PR)
        if: ${{ github.event_name == 'pull_request' }}
        shell: bash
        run: |
          echo "HEAD_COMMIT_SHA=$(git rev-parse origin/${{ github.base_ref }})" >> ${GITHUB_ENV}
      - name: Setup Environment (Push)
        if: ${{ github.event_name == 'push' || github.event_name == 'merge_group' }}
        shell: bash
        run: |
          echo "HEAD_COMMIT_SHA=$(git rev-parse origin/main)" >> ${GITHUB_ENV}
      - name: Check semver
        # uses: obi1kenobi/cargo-semver-checks-action@v2
        uses: n0-computer/cargo-semver-checks-action@feat-baseline
        with:
          package: iroh-gossip
          baseline-rev: ${{ env.HEAD_COMMIT_SHA }}
          use-cache: false

  check_fmt:
    timeout-minutes: 30
    name: Checking fmt
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
    steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt
    - uses: mozilla-actions/sccache-action@v0.0.9
    - uses: taiki-e/install-action@cargo-make
    - run: cargo make format-check

  check_docs:
    timeout-minutes: 30
    name: Checking docs
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
    steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@master
      with:
        toolchain: nightly-2024-11-30
    - name: Install sccache
      uses: mozilla-actions/sccache-action@v0.0.9

    - name: Docs
      run: cargo doc --workspace --all-features --no-deps --document-private-items
      env:
        RUSTDOCFLAGS: --cfg iroh_docsrs -Dwarnings

  clippy_check:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
    steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@stable
      with:
        components: clippy
    - name: Install sccache
      uses: mozilla-actions/sccache-action@v0.0.9

    # TODO: We have a bunch of platform-dependent code so should
    #    probably run this job on the full platform matrix
    - name: clippy check (all features)
      run: cargo clippy --workspace --all-features --all-targets --bins --tests --benches

    - name: clippy check (no features)
      run: cargo clippy --workspace --no-default-features --lib --bins --tests

    - name: clippy check (default features)
      run: cargo clippy --workspace --all-targets

  msrv:
    if: "github.event_name != 'pull_request' || ! contains(github.event.pull_request.labels.*.name, 'flaky-test')"
    timeout-minutes: 30
    name: Minimal Supported Rust Version
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
    steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@master
      with:
        toolchain: ${{ env.MSRV }}
    - name: Install sccache
      uses: mozilla-actions/sccache-action@v0.0.9

    - name: Check MSRV all features
      run: |
        cargo +$MSRV check --workspace --all-targets

  cargo_deny:
    timeout-minutes: 30
    name: cargo deny
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: EmbarkStudios/cargo-deny-action@v2
        with:
          arguments: --workspace --all-features
          command: check
          command-arguments: "-Dwarnings"

  codespell:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - run: pip install --user codespell[toml]
    - run: codespell --ignore-words-list=ans,atmost,crate,inout,ratatui,ser,stayin,swarmin,worl --skip=CHANGELOG.md
