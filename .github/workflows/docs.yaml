name: Docs Preview

on:
  pull_request:
  workflow_dispatch:
    inputs:
      pr_number:
        required: true
        type: string

# ensure job runs sequentially so pushing to the preview branch doesn't conflict
concurrency:
  group: ci-docs-preview

env:
  IROH_FORCE_STAGING_RELAYS: "1"

jobs:
  preview_docs:
    permissions: write-all
    timeout-minutes: 30
    name: Docs preview
    if: ${{ (github.event_name == 'pull_request' || github.event_name == 'workflow_dispatch' ) && !github.event.pull_request.head.repo.fork }}
    runs-on: ubuntu-latest
    env:
      RUSTC_WRAPPER: "sccache"
      SCCACHE_GHA_ENABLED: "on"
      SCCACHE_CACHE_SIZE: "50G"
      PREVIEW_PATH: pr/${{ github.event.pull_request.number || inputs.pr_number }}/docs

    steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@master
      with:
        toolchain: nightly-2024-11-30
    - name: Install sccache
      uses: mozilla-actions/sccache-action@v0.0.9

    - name: Generate Docs
      run: cargo doc --workspace --all-features --no-deps
      env:
        RUSTDOCFLAGS: --cfg iroh_docsrs

    - name: Deploy Docs to Preview Branch
      uses: peaceiris/actions-gh-pages@v4
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./target/doc/
        destination_dir: ${{ env.PREVIEW_PATH }}
        publish_branch: generated-docs-preview

    - name: Find Docs Comment
      uses: peter-evans/find-comment@v3
      id: fc
      with:
        issue-number: ${{ github.event.pull_request.number || inputs.pr_number }}
        comment-author: 'github-actions[bot]'
        body-includes: Documentation for this PR has been generated

    - name: Get current timestamp
      id: get_timestamp
      run: echo "TIMESTAMP=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_ENV

    - name: Create or Update Docs Comment
      uses: peter-evans/create-or-update-comment@v4
      with:
        issue-number: ${{ github.event.pull_request.number || inputs.pr_number }}
        comment-id: ${{ steps.fc.outputs.comment-id }}
        body: |
          Documentation for this PR has been generated and is available at: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/${{ env.PREVIEW_PATH }}/iroh_gossip/

          Last updated: ${{ env.TIMESTAMP }}
        edit-mode: replace
