## Description

<!-- A summary of what this pull request achieves and a rough list of changes. -->

## Breaking Changes

<!-- Optional, if there are any breaking changes document them, including how to migrate older code. -->

## Notes & open questions

<!-- Any notes, remarks or open questions you have to make about the PR. -->

## Change checklist

- [ ] Self-review.
- [ ] Documentation updates following the [style guide](https://rust-lang.github.io/rfcs/1574-more-api-documentation-conventions.html#appendix-a-full-conventions-text), if relevant.
- [ ] Tests if relevant.
- [ ] All breaking changes documented.
