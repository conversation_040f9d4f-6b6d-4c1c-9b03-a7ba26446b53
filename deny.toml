[advisories]
ignore = [
    "RUSTSEC-2024-0384",
    "RUSTSEC-2024-0436",
    "RUSTSEC-2023-0089",
]

[bans]
deny = [
    "aws-lc",
    "aws-lc-rs",
    "aws-lc-sys",
    "native-tls",
    "openssl",
]
multiple-versions = "allow"

[licenses]
allow = [
    "Apache-2.0",
    "Apache-2.0 WITH LLVM-exception",
    "BSD-2-Clause",
    "BSD-3-Clause",
    "BSL-1.0",
    "CDLA-Permissive-2.0",
    "ISC",
    "MIT",
    "Zlib",
    "Unicode-3.0",
    "MPL-2.0",
    "Unlicense",
]

[[licenses.clarify]]
expression = "MIT AND ISC AND OpenSSL"
name = "ring"

[[licenses.clarify.license-files]]
hash = **********
path = "LICENSE"
