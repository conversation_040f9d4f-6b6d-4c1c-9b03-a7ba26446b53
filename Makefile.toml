# Use cargo-make to run tasks here: https://crates.io/crates/cargo-make

[tasks.format]
workspace = false
command = "cargo"
args = [
     "fmt",
     "--all",
     "--",
     "--config",
     "unstable_features=true",
     "--config",
     "imports_granularity=Crate,group_imports=StdExternalCrate,reorder_imports=true",
]

[tasks.format-check]
workspace = false
command = "cargo"
args = [
     "fmt",
     "--all",
     "--check",
     "--",
     "--config",
     "unstable_features=true",
     "--config",
     "imports_granularity=Crate,group_imports=StdExternalCrate,reorder_imports=true",
]
